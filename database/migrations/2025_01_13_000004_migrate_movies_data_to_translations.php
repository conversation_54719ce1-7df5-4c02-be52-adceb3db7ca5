<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class MigrateMoviesDataToTranslations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Migrate existing data to Greek translations
        DB::statement('
            INSERT INTO movies_translations (movie_id, locale, title, synopsis, created_at, updated_at)
            SELECT 
                id,
                "el" as locale,
                title,
                synopsis,
                created_at,
                updated_at
            FROM movies
            WHERE title IS NOT NULL AND title != ""
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove Greek translations and restore data to main table
        DB::statement('
            UPDATE movies 
            SET 
                title = (
                    SELECT title 
                    FROM movies_translations 
                    WHERE movies_translations.movie_id = movies.id 
                    AND movies_translations.locale = "el" 
                    LIMIT 1
                ),
                synopsis = (
                    SELECT synopsis 
                    FROM movies_translations 
                    WHERE movies_translations.movie_id = movies.id 
                    AND movies_translations.locale = "el" 
                    LIMIT 1
                )
        ');
        
        // Delete all translations
        DB::table('movies_translations')->delete();
    }
}
