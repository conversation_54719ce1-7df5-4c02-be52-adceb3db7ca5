@extends('components.layout')
@section('header')
@stop
@section('content')

    @include('movies.partials._showProfileMin', ['page_title' => $data['page_title']])

    <div class="container    ">
        <div class="row  ">
            <div class="col-md-12">
                <div class="row   no-gutters">
                    <div class="col-md-9">
{{--                        <livewire:serve-ads lazy/>--}}
                        <div class="layout-page__content layout-page__section ">
                            {{--<div>{{ $data['page_subheading'] }}</div>--}}
                            @include('movies.partials._showMedia')
                        </div>
                        @if($movie->videos()->count() >= 1)
                            <div class="layout-page__content layout-page__section ">
                                <a name="videos"></a>
                                @include('movies.partials._showVideos')
                            </div>
                        @endif
                        @if ($movie->synopsis)
                            <div class="layout-page__content layout-page__section ">
                            <h2 class="play-persons__title">{!! trans('plays.show_media.synopsis_title') !!}</h2>
                                {!! \App\Helpers\ContentSanitizer::sanitizeHtml($movie->synopsis) !!}
                            </div>
                        @endif
                    </div>
                    <div class="col-md-3">
                        <div class="layout-page__sidebar  ">
                            @include('movies.partials._showSidebarMenu')
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    {!! Minify::javascript(array('/js/image-lightbox/imagelightbox.min.js', '/js/frontend/jquery.initImageLighbox.js'))->withFullUrl() !!}
@stop
