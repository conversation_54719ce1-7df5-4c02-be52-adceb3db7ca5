<?php

namespace App\Queries\Movies;

use App\Models\Movie;
use App\Queries\BaseQuery as Query;

class ShowMovieQuery extends Query
{
    /**
     * Declare the body of this query
     *
     * @return mixed
     *
     */
    public static function body($slug)
    {
        $movie = Movie::where('slug', $slug)
                    ->where('published', true)
                    ->with('translations')
                    ->with('mainImage')
                    ->with('images')
                    ->with('images.taggedPeople')
                    ->with('images.creditedPeople')
                    ->with('directors.mainImage')
                    ->with('actors.mainImage')
                    ->with('filmGenres')
//                    ->with('criticReviews')
//                    ->with('reviews')
                    ->firstOrFail();

        // We will load roles and people for these roles after the movie model has been retrieved
        // We are doing lazy eager loading because we need movie id for properly reading data
        // from movie_person_role table
        $movie->load([
            'roles.moviePeople' => function ($query) use ($movie) {
                $query
                    ->whereNotIn('movie_person_role.role_id', ['1', '2'])
                    ->wherePivot('movie_id', '=', $movie->id);
            },
        ]);

        return $movie;
    }

}
