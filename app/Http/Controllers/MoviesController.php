<?php

namespace App\Http\Controllers;

use App\Models\Movie;
use App\Queries\Movies\ShowMovieQuery;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Artesaos\SEOTools\Facades\SEOTools as SEO;
use Illuminate\View\View;

class MoviesController extends Controller
{

    /**
     * Display the specified resource.
     *
     * @param $slug
     * @param Request $request
     * @return Factory|View
     */
    public function show($slug, Request $request): Factory|View
    {
        // gather data needed to be passed to view
        $movie = ShowMovieQuery::get($slug);
        $data['main_page_reviews'] = $movie->reviews()
            ->where('banned', false)
            ->orderBy('upvotes', 'desc')
            ->take(2)
            ->get();
        $data['reviews_count'] = $movie->getAllowedReviewsCount();

        // seo
        // create a "full title" to pass to the seo meta tags
        $movie_full_title = $movie->title . ' (' . trans('movies.show.meta_title_component') . ' ' . $movie->year . ')';

        SEO::setTitle($movie_full_title);

        if ( ! empty($movie->synopsis)) {
            SEO::setDescription(strip_tags($movie->synopsis));
        }
        else {
            $meta_description = sprintf(trans('movies.show.meta_description'), $movie->title . ' (' . $movie->year . ')');
            SEO::setDescription($meta_description);
        }

        $this->setOpenGraphData($movie, $request);

        // canonical url
        SEO::setCanonical($request->url());

        return view('movies.show', compact('movie', 'data'));
    }

    /**
     * Display the media for a play.
     *
     * @param $slug
     * @param Request $request
     * @return Factory|View
     */
    public function showMedia($slug, Request $request): Factory|View
    {
        $movie = ShowMovieQuery::get($slug);
        $data['reviews_count'] = $movie->getAllowedReviewsCount();

        // prepare view data
        // page title
        $data['page_title'] = trans('movies.show_media.page_title');

        // seo
        // create a "full title" to pass to the seo meta tags
        $movie_full_title = $movie->title . ' (' . trans('movies.show.meta_title_component') . ' ' . $movie->year . ')';

        SEO::setTitle(sprintf(trans('movies.show_media.meta_title'), $movie_full_title));
        SEO::setDescription(sprintf(trans('movies.show_media.meta_description'), $movie_full_title));

        $this->setOpenGraphData($movie, $request);

        // canonical url
        SEO::setCanonical($request->url());

        return view('movies.showMedia', compact('movie', 'data'));
    }

    /**
     * Display the info for a movie.
     *
     * @param $slug
     * @param Request $request
     * @return Factory|View
     */
    public function showInfo($slug, Request $request): Factory|View
    {
        $movie = ShowMovieQuery::get($slug);
        $data['reviews_count'] = $movie->getAllowedReviewsCount();

        // prepare view data
        // page title
        $data['page_title'] = trans('movies.show_info.page_title');

        // seo
        // create a "full title" to pass to the seo meta tags
        $movie_full_title = $movie->title . ' (' . trans('movies.show.meta_title_component') . ' ' . $movie->year . ')';

        SEO::setTitle(sprintf(trans('movies.show_info.meta_title'), $movie_full_title));
        SEO::setDescription(sprintf(trans('movies.show_info.meta_description'), $movie_full_title));

        $this->setOpenGraphData($movie, $request);

        // canonical url
        SEO::setCanonical($request->url());

        return view('movies.showInfo', compact('movie', 'data'));
    }

    /**
     * Display the user reviews for a movie.
     *
     * @param $slug
     * @param Request $request
     * @return Factory|View
     */
    public function showUserReviews($slug, Request $request): Factory|View
    {
        // gather data needed to be passed to view
        $movie = ShowMovieQuery::get($slug);
        $data['reviews'] = $movie->reviews()
            ->where('banned', false)
            ->orderBy('upvotes', 'desc')
            ->get();
        $data['reviews_count'] = $movie->getAllowedReviewsCount();

        // seo
        // page title
        $data['page_title']         = trans('movies.show_user_reviews.page_title');

        // seo
        // create a "full title" to pass to the seo meta tags
        $movie_full_title = $movie->title . ' (' . trans('movies.show.meta_title_component') . ' ' . $movie->year . ')';

        SEO::setTitle(sprintf(trans('movies.show_user_reviews.meta_title'), $movie_full_title));
        SEO::setDescription(sprintf(trans('movies.show_user_reviews.meta_description'), $movie_full_title));

        $this->setOpenGraphData($movie, $request);

        // canonical url
        SEO::setCanonical($request->url());

        return view('movies.showUserReviews', compact('movie', 'data'));
    }

    /**
     * Display the critic reviews for a play.
     *
     * @param $slug
     * @param Request $request
     * @return Factory|View
     */
    public function showCriticReviews($slug, Request $request): Factory|View
    {
        $play = null;

        $movie = ShowPlayQuery::get($slug);
        $data['reviews_count'] = $movie->getAllowedReviewsCount();

        // prepare view data
        // page title
        $data['page_title'] = trans('movies.show_references.page_title');

        // seo
        // create a "full title" to pass to the seo meta tags
        $movie_full_title = $movie->title . ' (' . trans('movies.show.meta_title_component') . ' ' . $movie->year . ')';

        SEO::setTitle(sprintf(trans('movies.show_references.meta_title'), $movie_full_title));
        SEO::setDescription(sprintf(trans('movies.show_references.meta_description'), $movie_full_title));

        $this->setOpenGraphData($movie, $request);

        // canonical url
        SEO::setCanonical($request->url());

        return view('movies.showCriticReviews', compact('play', 'data'));
    }

    private function setOpenGraphData(Movie $movie, $request): void
    {
        // og url
        SEO::opengraph()->setUrl($request->url());
        // og type
        SEO::opengraph()->setType('website');
        // og title tag for facebook
        $open_graph_title = $movie->title . ' (' . $movie->year . ')';
        SEO::opengraph()->setTitle($open_graph_title);
        // og image tag for facebook
        if (isset($movie->mainImage)) {
            SEO::opengraph()->addImage(unstageAsset($movie->mainImage->filename));
        } else {
            SEO::opengraph()->addImage(unstageAsset('unstage.jpg'));
        }
        // og site_name
        SEO::opengraph()->setSiteName('unstage.gr');
    }

}
