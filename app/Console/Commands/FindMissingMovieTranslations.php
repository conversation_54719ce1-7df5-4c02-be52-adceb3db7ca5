<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Movie;

class FindMissingMovieTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'movie-translations:find-missing {--locale=en} {--export}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find movies with missing translations';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $locale = $this->option('locale');
        $export = $this->option('export');
        
        $this->info("Finding movies missing {$locale} translations...");
        
        $missingTranslations = Movie::notTranslatedIn($locale)->get();
        $totalMovies = Movie::count();
        $translatedCount = $totalMovies - $missingTranslations->count();
        
        $this->info("Results:");
        $this->info("- Total Movies: {$totalMovies}");
        $this->info("- Translated ({$locale}): {$translatedCount}");
        $this->info("- Missing translations: {$missingTranslations->count()}");
        $this->info("- Translation coverage: " . round(($translatedCount / $totalMovies) * 100, 2) . "%");
        
        if ($export) {
            $filename = "missing_movie_translations_{$locale}_" . date('Y-m-d_H-i-s') . ".csv";
            $filepath = storage_path("app/{$filename}");
            
            $file = fopen($filepath, 'w');
            fputcsv($file, ['ID', 'Title (Greek)', 'Year', 'Created At']);
            
            foreach ($missingTranslations as $movie) {
                fputcsv($file, [
                    $movie->id,
                    $movie->translate('el')->title ?? 'N/A',
                    $movie->year,
                    $movie->created_at
                ]);
            }
            
            fclose($file);
            $this->info("Exported to: {$filepath}");
        }
        
        if ($missingTranslations->count() > 0) {
            $this->info("\nFirst 10 movies missing {$locale} translations:");
            $this->table(
                ['ID', 'Title (Greek)', 'Year'],
                $missingTranslations->take(10)->map(function ($movie) {
                    return [
                        $movie->id,
                        $movie->translate('el')->title ?? 'N/A',
                        $movie->year
                    ];
                })->toArray()
            );
        }
        
        return 0;
    }
}
