<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Movie;
use App\Jobs\UpdateMovieTranslation;
use Illuminate\Foundation\Bus\DispatchesJobs;

class UpdateMovieTranslations extends Command
{
    use DispatchesJobs;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'movie-translations:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Translates movie data automatically';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // fetch some movies
        $movies = Movie::notTranslatedIn('en')
            ->take(40)
            ->get();

        foreach($movies as $movie)
        {
            $this->dispatch(new UpdateMovieTranslation($movie));
        }
        
        $this->info('Dispatched ' . $movies->count() . ' movie translation jobs.');
    }
}
