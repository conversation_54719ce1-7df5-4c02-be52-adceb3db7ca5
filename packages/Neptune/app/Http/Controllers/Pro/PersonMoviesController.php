<?php

namespace Packages\Neptune\app\Http\Controllers\Pro;

use App\Exceptions\Pro\ProException;
use App\Mail\ProPublishMessage;
use App\Models\FilmGenre;
use App\Models\MoviePersonRole;
use App\Models\Person;
use App\Models\Movie;
use App\Trak\Cache\InvalidatorPersonCache;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Packages\Neptune\app\Http\Requests\Pro\PersonMoviesStoreRequest;
use Packages\Neptune\app\Http\Requests\Pro\PersonMoviesUpdateRequest;

class PersonMoviesController extends ProController
{

    public function __construct(InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct($cacheInvalidator);

        // redirect to proper page if adminised
        $this->middleware(function ($request, $next) {
            // $this->logged_user is filled in in the ProController constructor
            if($this->logged_user->adminised)
            {
                return redirect()->route('neptune.pro.adminised.plays.index');
            }

            return $next($request);
        });
    }

    /**
     * Show the form for previewing person movies
     *
     * @param  $person_slug
     */
    public function index($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;
        $this->view_data['movies']          = $this->logged_user->moviesCreated()->orderBy('created_at', 'desc')->paginate(10);

        return view('neptune::pro.movies.index', $this->view_data);
    }

    /**
     * Show the form for creating the pro person movie
     *
     * @param  $person_slug
     */
    public function create($person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->with('mainImage')->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        $this->view_data['selected_person'] = $person;

        return view('neptune::pro.movies.create', $this->view_data);
    }


    /**
     * Store the specified resource in storage.
     *
     * @param PersonMoviesStoreRequest $request
     * @param $person_slug
     * @throws PersonProException
     */
    public function store(PersonMoviesStoreRequest $request, $person_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        // Workaround to properly create slug
        $movie = Movie::create(['title' => trim($request->title), 'year' => trim($request->year)]);

        // ...set the user_id attribute for the movie
        $movie->user_id = $this->logged_user->id;

        // ...set the moderated attribute to false
        $movie->moderated = false;

        // ...set the finalised attribute to false
        $movie->finalised = false;

        // ...set the published attribute to false
        $movie->published = false;

        // ...and finaly save the model to persist the changes
        $movie->save();

        return redirect()->route('neptune.pro.movies.editInfo', [$person->slug, $movie->slug])
            ->with('success', trans('neptune::pro/movies.create.successful_creation'));
    }

    /**
     * Show the form for editing the pro person movie info
     *
     * @param  $person_slug
     * @param  $movie_slug
     * @throws PersonProException
     */
    public function editInfo($person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)
                ->firstOrFail()
                ->load('filmGenres');
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        $this->view_data['selected_person'] = $person;
        $this->view_data['movie']           = $movie;
        $this->view_data['roles']           = [];
        $this->view_data['filmGenres']      = [];

        // get all roles for movies
        foreach (config('roles')->where('for_movies', true) as $role_id => $role)
        {
            $this->view_data['roles'][$role_id] = $role->description;
        }

        // get all filmGenres
        foreach(FilmGenre::orderBy('name')->get() as $filmGenre)
        {
            $this->view_data['filmGenres'][$filmGenre->id] = $filmGenre->name;
        }

        // get role ids for the given movie for the given person
        $role_ids = MoviePersonRole::where('person_id', $person->id)
            ->where('movie_id', $movie->id)
            ->pluck('role_id');
        $this->view_data['selected_roles'] = $role_ids;

        return view('neptune::pro.movies.editInfo', $this->view_data);
    }

    /**
     * Show the form for editing the pro person movie images
     *
     * @param  $person_slug
     * @param  $movie_slug
     * @throws PersonProException
     */
    public function editImages($person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        $this->view_data['selected_person'] = $person;
        $this->view_data['movie']           = $movie;

        return view('neptune::pro.movies.editImages', $this->view_data);
    }

    /**
     * Show the form for editing the pro movie person roles
     *
     * @param  $person_slug
     * @param  $movie_slug
     * @throws PersonProException
     */
    public function editRoles($person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProException
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)
                ->with('roles')
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        $movie->load([
            'roles.moviePeople' => function ($query) use ($movie) {
                $query->wherePivot('movie_id', '=', $movie->id);
            },
        ]);

        $rolesWithoutActors = [];
        // Workaround to get list of roles for which this movie
        // doesn't have any person
        $rolesIdsWithActors = [];
        foreach ($movie->roles as $movieRole)
        {
            $rolesIdsWithActors[] = $movieRole->id;
        }
        foreach (config('roles')->where('for_movies', true) as $role)
        {
            if ( ! in_array($role->id, $rolesIdsWithActors))
            {
                $rolesWithoutActors[] = $role;
            }
        }

        $this->view_data['selected_person']     = $person;
        $this->view_data['movie']               = $movie;
        $this->view_data['roles']               = config('roles')->where('for_movies', true)->sortBy('sort_order');
        $this->view_data['rolesWithoutActors']  = $rolesWithoutActors;

        return view('neptune::pro.movies.editRoles', $this->view_data);
    }

    /**
     * Show the form for editing the pro person movie private note
     *
     * @param  $person_slug
     * @param  $movie_slug
     * @throws PersonProException
     */
    public function editNotes($person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        $this->view_data['selected_person'] = $person;
        $this->view_data['movie']            = $movie;

        return view('neptune::pro.movies.editNotes', $this->view_data);
    }

    /**
     * Show the form for finalising and publishing the pro person movie
     *
     * @param  $person_slug
     * @param  $movie_slug
     * @throws PersonProException
     */
    public function editPublished($person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)
                ->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        $this->view_data['selected_person'] = $person;
        $this->view_data['movie']           = $movie;

        // check if the person has given his own role in the movie
        $existing_roles = MoviePersonRole::where('person_id', $person->id)
            ->where('movie_id', $movie->id)
            ->get();

        if($existing_roles->isEmpty())
        {
            return view('neptune::pro.movies.editPublished', $this->view_data)
                ->withErrors(['own_role_status' => trans('neptune::pro/movies.edit_published.own_role_error_msg')]);
        }

        return view('neptune::pro.movies.editPublished', $this->view_data);
    }


    /**
     * Update the specified resource in storage.
     *
     */
    public function updatePublished(Request $request, $person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        // check if the person has given his own role in the movie
        $existing_roles = MoviePersonRole::where('person_id', $person->id)
            ->where('movie_id', $movie->id)
            ->get();

        if($existing_roles->isEmpty())
        {
            return redirect()->route('neptune.pro.movies.editPublished', [$person->slug, $movie->slug])
                ->withErrors(['own_role_status' => trans('neptune::pro/movies.edit_published.own_role_error_msg')]);
        }

        // manually set the fields
        $movie->moderated = true;
        $movie->published = true;

        $movie->save();

        try {
            // send notification email
            $input = [
                'person_id'         => $person->id,
                'person_name'       => $person->fullName,
                'person_slug'       => $person->slug,
                'user_id'           => $this->logged_user->id,
                'user_email'        => $this->logged_user->email,
                'endeavour_type'    => 'movie',
                'endeavour_id'      => $movie->id,
                'endeavour_title'   => $movie->title,
                'endeavour_slug'    => $movie->slug,
            ];
            Mail::to(config('mail_addresses.contact'))->send(new ProPublishMessage($input));
        } catch (\Exception $exception) {
            report($exception);
        }

        return redirect()->route('neptune.pro.movies.index', [$person->slug, $movie->slug])
            ->with('success', trans('neptune::pro/movies.edit_published.successful_publish'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param PersonMoviesUpdateRequest $request
     */
    public function updateInfo(PersonMoviesUpdateRequest $request, $person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        // manually set the editable fields
        // synopsis
        if($request->has('synopsis') && ! empty($request->input('synopsis')))
        {
            $movie->translateOrNew('el')->synopsis = trim($request->input('synopsis'));
        }

        // ...we also want to tag the movie fer moderation
        // ...(ideally on the condition that some data changed)
        if($movie->isDirty())
        {
            $movie->moderated = false;
        }

        $movie->save();

        // handle filmGenres
        // code to filter out the empty string value submitted because of the placeholder of the dropdown
        $genre_collection = new Collection($request->input('filmGenre'));
        $filtered_genre_collection = $genre_collection->filter(function ($value, $key) {
            return $value != '';
        });
        $movie->filmGenres()->sync($filtered_genre_collection);

        // handle own roles
        // firstly clear out any roles fer this movie fer this person
        $existing_roles = MoviePersonRole::where('person_id', $person->id)
            ->where('movie_id', $movie->id)
            ->delete();

        // ...and then create the submitted roles
        foreach($request->input('own_role') as $role_id)
        {
            if( ! empty($role_id) )
            {
                // character handling for actor role
                if($role_id == 1 && $request->has('character') && ! empty($request->input('character')))
                {
                    $data = [
                        'role_id'   => $role_id,
                        'movie_id'  => $movie->id,
                        'person_id' => $person->id,
                        'character' => trim($request->input('character')),
                    ];
                }
                else
                {
                    $data = [
                        'role_id'   => $role_id,
                        'movie_id'  => $movie->id,
                        'person_id' => $person->id,
                    ];
                }

                MoviePersonRole::create($data);

            }
        }

        return redirect()->route('neptune.pro.movies.editInfo', [$person->slug, $movie->slug])
            ->with('success', trans('neptune::pro/movies.edit_info.successful_update'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     */
    public function updateNotes(Request $request, $person_slug, $movie_slug)
    {
        // get the person
        try
        {
            $person = Person::where('slug', $person_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Person not found',
                ]
            ]);
        }

        // throws ProExcpetion
        $this->personChecks($person);

        // get the movie
        try
        {
            $movie = Movie::where('slug', $movie_slug)->firstOrFail();
        }
        catch(ModelNotFoundException $e)
        {
            return redirect()->route('neptune.pro.home')->with([
                \App\Components\Flash::MESSAGE_SESSION_KEY => [
                    'status' => 'error',
                    'title' => 'An error occured',
                    'body' => 'Movie was not found',
                ]
            ]);
        }

        // throws ProException
        $this->movieChecks($person, $movie);

        // manually set the editable fields
        // user note
        if($request->has('user_notes') && ! empty($request->input('user_notes')))
        {
            $movie->user_notes = trim($request->input('user_notes'));
        }

        // ...we also want to tag the movie fer moderation
        // ...(on the condition that some data changed)
        if($movie->isDirty())
        {
            $movie->moderated = false;
        }

        $movie->save();

        return redirect()->route('neptune.pro.movies.editNotes', [$person->slug, $movie->slug])
            ->with('success', trans('neptune::pro/movies.edit_notes.successful_update'));
    }


    /**
     * @param Person $person
     * @param Movie $movie
     * @throws ProException
     */
    protected function movieChecks(Person $person, Movie $movie)
    {
        // if the movie is not created by the logged in user, abort
        if( $this->logged_user->id != $movie->user_id )
        {
            throw new ProException('Insufficient permissions to edit the movie');
        }

        // if the movie is moderated, abort
        if( $movie->moderated )
        {
            throw new ProException('Insufficient permissions to edit the movie');
        }
    }
}
