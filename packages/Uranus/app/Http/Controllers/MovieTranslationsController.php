<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Movie;
use Aws\Exception\AwsException;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Controllers\Controller;

class MovieTranslationsController extends Controller
{
    private $translationClient;
    private $sourceLang;
    private $targetLang;

    public function __construct()
    {
        $this->translationClient = new \Aws\Translate\TranslateClient([
            'region' => 'eu-west-1',
            'version' => '2017-07-01'
        ]);

        $this->sourceLang = 'el';
        $this->targetLang = 'en';
    }

    /**
     * Suggest translation for movie title or synopsis
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function suggest(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $movie = Movie::findOrFail($request->input('movie_id'));
        $field = $request->input('field', 'title');

        if (empty($movie->$field)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate'
            ]);
        }

        // Strip HTML tags for translation
        $textToTranslate = strip_tags($movie->$field);

        if (empty($textToTranslate)) {
            return response()->json([
                'success' => false,
                'message' => 'No content to translate after removing HTML tags'
            ]);
        }

        // AWS imposes a request limit for 5000 bytes of translatable text
        if (mb_strlen($textToTranslate) >= 2000) {
            return response()->json([
                'success' => false,
                'message' => 'Content too long for translation'
            ]);
        }

        try {
            $result = $this->translationClient->translateText([
                'SourceLanguageCode' => $this->sourceLang,
                'TargetLanguageCode' => $this->targetLang,
                'Text' => $textToTranslate,
            ]);

            return response()->json([
                'success' => true,
                'translation' => $result->get('TranslatedText')
            ]);

        } catch (AwsException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Translation service error: ' . $e->getMessage()
            ]);
        }
    }
}
