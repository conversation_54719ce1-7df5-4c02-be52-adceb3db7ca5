@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css" media="screen"/>
@stop
@section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$person->fullName}}</h1>
            <h4>
                <a target="_blank" href="{{ route('uranus.people.translatable.edit', $person->id) }}">
                    {{ $person->hasTranslation('en') ? $person->translate('en')->first_name . ' ' . $person->translate('en')->last_name . ' (en)' : "No (en) name" }}
                </a>
            </h4>
            <div class="page-header-actions">
                <button class="btn btn-floating btn-primary" type="button" data-toggle="modal" data-target="#informationModal">
                    <i class="icon wb-help" aria-hidden="true"></i></button>

                <a href="{{ route('uranus.person.revisions',$person->id) }}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::common.revisions_tooltip') !!}">
                        <i class="fa fa-lg fa-history red-600" style="font-size: 24px"></i></button>
                </a>
                <a target="_blank" href="{!! route('people.show', ['slug' => $person->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('person_details', 'uranus_person_edit')]) !!}">
                    <button type="button" class="btn btn-floating btn-primary" title="{!! trans('uranus::common.preview_tooltip') !!}">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.people.castings', $person->id) !!}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::common.castings_tooltip') !!}">
                        <i class="fa fa-lg fa-film green-600" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.people.analytics', $person->id) !!}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::people.analytics_tooltip') !!}">
                        <i class="fa fa-lg fa-university green-600" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.people.rankingHistory', $person->id) !!}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::people.rankings_tooltip') !!}">
                        <i class="fa fa-lg fa-list-ol green-600" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.personReferences.index', $person->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::common.personReferences_list_tooltip') !!}"><i class="icon wb-chat"
                                                                                                                                                    aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.people.translatable.edit', $person->id) !!}" title="{!! trans('uranus::people.translatable_tooltip') !!}">
                    <button type="button" class="btn btn-floating btn-success">
                        <i class="icon wb-flag" aria-hidden="true"></i></button>
                </a>
                @admincan('manage_pro')
                <a href="{!! route('uranus.people.proUsers', $person->id) !!}">
                    <button title="Preview pro users related to person" type="button" class="btn btn-floating btn-primary">
                        <i class="icon wb-user" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.people.reslug.edit', $person->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::people.reslug_tooltip') !!}">
                        <i class="icon wb-link-broken" aria-hidden="true"></i></button>
                </a>
                @endadmincan
                <a href="{!! route('uranus.people.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger">
                        <i class="icon wb-plus" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <p class="text-muted" style="margin-bottom: 15px;">
                    <strong>Slug:</strong> <code>{{ $person->slug }}</code>
                </p>
                <form method="post" action="{{ route('uranus.people.update',$person->id) }}" enctype="multipart/form-data">
                    {!! csrf_field() !!}
                    {!! method_field('put') !!}
                    @include('uranus::people._peopleForm', ['submitButtonText'=>'Αποθήκευση'])
                </form>
            </div>
        </div>
    </div>
    <!-- End Page -->
    <div id="informationModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="informationModal" aria-hidden="true" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button class="close" aria-label="Close" data-dismiss="modal" type="button">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Χρήσιμες συμβουλές</h4>
                </div>
                <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="alert dark alert-primary" role="alert">
                                    <h4>'Ονομα - Επώνυμο</h4>
                                    <p> Το πεδίο "'Ονομα" <b>δεν</b> είναι υποχρεωτικό.<br>
                                        Για συντελεστές που έχουμε μόνο το καλλιτεχνικό τους όνομα (πχ Μόνικα, Μαρινέλλα) η πληροφορία αυτή να καταχωρείται στο πεδίο "Επώνυμο". </p>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js" charset="UTF-8"></script>
    <script type="text/javascript">
        $(function ()
        {
            $('#subscription_id').select2({
            placeholder: 'Choose a subscription'
            });

            $('#school_list').select2({
            placeholder: 'Choose a school'
            });

            $('#award_list').select2({
            placeholder: 'Choose an award'
            });

            $('#album_list').select2({
            placeholder: 'Choose an album'
            });

            $('#book_list').select2({
            placeholder: 'Choose a book'
            });
        });
    </script>
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
