@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
            href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
            media="screen"/>
{{--    <link rel="stylesheet" href="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.css')}}">--}}
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$endeavour->title}}</h1>
            <h4>
                {{--<a target="_blank" href="{{ route('uranus.plays.translatable.edit', $play->id) }}">--}}
                    {{--{{ $play->hasTranslation('en') ? $play->translate('en')->title . ' (en)' : "No (en) title" }}--}}
                {{--</a>--}}
            </h4>
            <div class="page-header-actions">

                {{--<a href="{{ route('uranus.play.revisions',$play->id) }}">--}}
                    {{--<button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::common.revisions_tooltip') !!}">--}}
                        {{--<i class="fa fa-lg fa-history blue-600" style="font-size: 24px"></i></button>--}}
                {{--</a>--}}
                <a target="_blank" href="{!! route('endeavours.show', $endeavour->slug) !!}">
                    <button type="button" class="btn btn-floating btn-primary" title="{!! trans('uranus::common.preview_tooltip') !!}">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.endeavours.characters.edit', $endeavour->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::endeavours.characters') !!}"><i class="icon wb-user"
                                                                                                                                    aria-hidden="true"></i></button>
                </a>
                {{--<a href="{!! route('uranus.plays.create.replica', $play->id) !!}">--}}
                    {{--<button type="button" class="btn btn-floating btn-warning" title="{!! trans('uranus::plays.edit_replicate_tooltip') !!}"><i class="icon wb-copy"--}}
                                {{--aria-hidden="true"></i></button>--}}
                {{--</a>--}}
                {{--<a href="{!! route('uranus.criticReviews.index', $play->id) !!}">--}}
                    {{--<button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.criticReviews_list_tooltip') !!}"><i class="icon wb-star"--}}
                                {{--aria-hidden="true"></i></button>--}}
                {{--</a>--}}
                {{--<a href="{!! route('uranus.play.timetable.show', $play->id) !!}">--}}
                    {{--<button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.edit_calendar') !!}"><i class="icon wb-calendar"--}}
                                {{--aria-hidden="true"></i></button>--}}
                {{--</a>--}}
                {{--<a href="{!! route('uranus.plays.translatable.edit', $play->id) !!}">--}}
                    {{--<button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.edit_translatable') !!}"><i class="icon wb-flag"--}}
                                {{--aria-hidden="true"></i></button>--}}
                {{--</a>--}}
                <a href="{!! route('uranus.endeavours.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger" title="Νέο endeavour"><i class="icon wb-plus"
                                aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <form action="{{route('uranus.endeavours.update', $endeavour->id)}}" method="post" enctype="multipart/form-data">
                            <div class="panel">
                                <div class="panel-heading">
                                    <h4 class="panel-title">Γενικές πληροφορίες</h4>
                                </div>
                                <div class="panel-body container-fluid">
                                    <p class="text-muted" style="margin-bottom: 15px;">
                                        <strong>Slug:</strong> <code>{{ $endeavour->slug }}</code>
                                    </p>
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    @include('uranus::endeavours._endeavourForm', ['submitButtonText'=>'Αποθήκευση'])
                                </div>
                            </div>
                            <!-- End Panel Static Lables -->

                            @include('uranus::_partials._tagSelector', ['model' => $endeavour])
                        </form>

                    </div>
                    <div class="col-sm-6">
                        <!-- Panel Floating Lables -->
                        <div class="panel">
                            @include('uranus::images.manage')
                        </div>
                        <!-- End Panel Floating Lables -->
                        <!-- Panel Floating Lables -->

                        @include('uranus::_partials._personCreator')

                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Συντελεστές</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                @include('uranus::endeavours._endeavourRolesSelects')
                            </div>
                        </div>

                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Digital Platform Urls</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                <div>
                                    <a href="{{ route('uranus.digital_platform_urls.create', ['endeavour_id' => $endeavour->id]) }}"><p>Δημιουργία νέου</p></a>
                                </div>
                                <div>
                                    <table class="table">
                                        @forelse($endeavour->digitalPlatformUrls as $dpu)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('uranus.digital_platform_urls.edit',$dpu->id) }}"><p>{!! $dpu->title !!}</p></a>
                                                </td>
                                                <td>
                                                    {!! wordwrap($dpu->url, 30, "<br />\n", true) !!}
                                                </td>
                                                <td>
                                                    <a data-url="{!! route('uranus.digital_platform_urls.destroy', $dpu->id) !!}" href="" class="deleteResource">
                                                        <button type="button" class="btn btn-icon btn-danger btn-outline">
                                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                                    </a>
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop

@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop

@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>
    <script>
        $('#tag_list').select2({
            placeholder: 'Choose a tag'
        });
    </script>
    <script src="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.js')}}"></script>
    <script src="{{asset('assets/js/components/bootstrap-datepicker.js')}}"></script>
@stop
