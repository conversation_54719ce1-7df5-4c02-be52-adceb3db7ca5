<div class="form-group row">
    <div class="col-sm-12">
        <div class="checkbox-custom checkbox-primary">
            <input id="published" name="published" type="checkbox" {{ old('published',$movie->published)?'checked="checked"':'' }}>
            <label for="published">Δημοσιευμένη ταινία</label>
        </div>
        <div class="checkbox-custom checkbox-primary orange-600">
            <input id="finalised" name="finalised" type="checkbox" {{ old('finalised',$movie->finalised)?'checked="checked"':'' }}>
            <label for="finalised">Τετελεσμένη ταινία</label>
        </div>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 orange-600">
        <label for="notes" class="control-label">Notes (εσωτερικής χρήσεως):</label>
        <textarea name="notes" class="form-control">{{ old('notes',$movie->notes) }}</textarea>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12 col-md-12">
        <label for="film_genre_list" class="control-label">Είδος:</label>
        <select name="film_genre_list[]" id="film_genre_list" class="form-control" multiple>
            @foreach($filmGenres as $filmGenre)
                <option value="{{ $filmGenre->id }}" {{ in_array($filmGenre->id,$movie->filmGenresIds) || (old('film_genre_list') && in_array($filmGenre->id,old('film_genre_list')))?'selected="selected"':''}}>
                    {{ $filmGenre->name }}
                </option>
            @endforeach
        </select>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12">
        <label for="imdb" class="control-label">imdb page:</label>
        <input type="text" class="form-control" name="imdb" id="imdb" value="{{ old('imdb',$movie->imdb) }}">
    </div>
</div>
<br>
<h4>Υπόλοιπες πληροφορίες</h4>
<div class="form-group row">
    <div class="col-sm-12  col-md-6">
        <label for="year" class="control-label">Έτος Έναρξης:</label>
        <input type="number" name="year" id="year" value="{{ old('year',$movie->year) }}" class="form-control">
    </div>
    <div class="col-sm-12  col-md-6">
        <label for="duration" class="control-label">Διάρκεια:</label>
        <input type="number" name="duration" id="duration" value="{{ old('duration',$movie->duration) }}" class="form-control">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 col-md-6 col-md-offset-3">
        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
    </div>
</div>

@include('uranus::_partials._tagSelector', ['model' => $movie])

<br>
@if($movie->user_id != null)
<h4>Πληροφορίες χρήστου καταχωρητού</h4>
<div class="form-group row">
    <div class="col-sm-12">
        <p class="control-label">User: {{ $movie->user->fullname }} (id: {{ $movie->user->id }})</p>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 orange-600">
        <label for="moderated" class="control-label">Moderated:</label>
        <select name="moderated" id="moderated" class="form-control">
            <option value="0" {{ $movie->moderated ? 'selected': '' }}>Οχι</option>
            <option value="1" {{ $movie->moderated ? 'selected': '' }}>Ναι</option>
        </select>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12 orange-600">
        <label class="control-label">User Notes (δεν αποθηκεύονται από εμάς):</label>
        <textarea disabled class="form-control wysiwyg">{!! $movie->user_notes !!}</textarea>
    </div>
</div>
@endif


@section('footer')
    @parent
    <script type="text/javascript" src="{{ asset("js/admin/tinymce/tinymce.min.js") }}"></script>
    <script type="text/javascript">
        tinymce.init({
            selector: ".wysiwyg",
            plugins: [
                'advlist autolink lists link charmap preview anchor',
                'paste code fullscreen hr'
            ],
            entity_encoding: "raw",
            paste_auto_cleanup_on_paste: true,
            paste_preprocess: function (pl, o)
            {
                // Content string containing the HTML from the clipboard
                o.content = o.content;
            },
            paste_postprocess: function (pl, o)
            {
                // Content DOM node containing the DOM structure of the clipboard
                o.node.innerHTML = o.node.innerHTML;
            }
        });

        $('#tag_list').select2({
            placeholder: 'Choose a tag'
        });
    </script>
@stop
