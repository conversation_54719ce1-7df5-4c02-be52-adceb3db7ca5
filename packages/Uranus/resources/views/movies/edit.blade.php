@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
            href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
            media="screen"/>
    <link rel="stylesheet" href="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.css')}}">
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$movie->title}}</h1>
            <h4>
                <a target="_blank" href="{{ route('uranus.movies.translatable.edit', $movie->id) }}">
                    {{ $movie->hasTranslation('en') ? $movie->translate('en')->title . ' (en)' : "No (en) title" }}
                </a>
            </h4>
            <div class="page-header-actions">

                <a href="{{ route('uranus.movie.revisions',$movie->id) }}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::common.revisions_tooltip') !!}">
                        <i class="fa fa-lg fa-history blue-600" style="font-size: 24px"></i></button>
                </a>
                <a target="_blank" href="{!! route('movies.show', ['slug' => $movie->slug]) !!}">
                    <button type="button" class="btn btn-floating btn-primary" title="{!! trans('uranus::common.preview_tooltip') !!}">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.images.moderation.index', ['movie_id' => $movie->id]) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::movies.images') !!}"><i class="icon wb-image"
                                                                                                                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.movies.translatable.edit', $movie->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::movies.edit_translatable') !!}"><i class="icon wb-flag"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.movies.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger" title="{!! trans('uranus::movies.create_movie_tooltip') !!}"><i class="icon wb-plus"
                                aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Γενικές πληροφορίες ταινίας</h4>
                            </div>
                            <div class="panel-body container-fluid">
                                <p class="text-muted" style="margin-bottom: 15px;">
                                    <strong>Slug:</strong> <code>{{ $movie->slug }}</code>
                                </p>
                                <form action="{{route('uranus.movies.update', $movie->id)}}" method="post" enctype="multipart/form-data">
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    @include('uranus::movies._movieForm', ['submitButtonText'=>'Αποθήκευση ταινίας'])
                                </form>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <div class="col-sm-6">
                        <!-- Panel Floating Lables -->
                        <div class="panel">
                            @include('uranus::images.manage')
                        </div>

                        @include('uranus::_partials._personCreator')

                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Συντελεστές ταινίας</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                @include('uranus::movies._movieRolesSelects', ['roles' => config('roles')->where('for_movies', true)->sortBy('sort_order')])
                            </div>
                        </div>
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>
@stop
