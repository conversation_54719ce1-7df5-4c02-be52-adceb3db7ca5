$(document).ready(function() {
    
    // Handle title translation
    $('#translate_title').click(function(e) {
        e.preventDefault();
        
        var url = $(this).data('url');
        var movieId = window.location.pathname.split('/').slice(-2, -1)[0];
        
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                movie_id: movieId,
                field: 'title',
                _token: $('input[name="_token"]').val()
            },
            beforeSend: function() {
                $('#translate_title').text('Μεταφράζεται...');
            },
            success: function(response) {
                if (response.success) {
                    $('#title_en').val(response.translation);
                    $('#translate_title').text('Πρότεινε μετάφρασιν');
                } else {
                    alert('Σφάλμα: ' + response.message);
                    $('#translate_title').text('Πρότεινε μετάφρασιν');
                }
            },
            error: function() {
                alert('Σφάλμα κατά τη μετάφραση');
                $('#translate_title').text('Πρότεινε μετάφρασιν');
            }
        });
    });
    
    // Handle synopsis translation
    $('#translate_synopsis').click(function(e) {
        e.preventDefault();
        
        var url = $(this).data('url');
        var movieId = window.location.pathname.split('/').slice(-2, -1)[0];
        
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                movie_id: movieId,
                field: 'synopsis',
                _token: $('input[name="_token"]').val()
            },
            beforeSend: function() {
                $('#translate_synopsis').text('Μεταφράζεται...');
            },
            success: function(response) {
                if (response.success) {
                    $('#synopsis_en').val(response.translation);
                    $('#translate_synopsis').text('Πρότεινε μετάφρασιν');
                } else {
                    alert('Σφάλμα: ' + response.message);
                    $('#translate_synopsis').text('Πρότεινε μετάφρασιν');
                }
            },
            error: function() {
                alert('Σφάλμα κατά τη μετάφραση');
                $('#translate_synopsis').text('Πρότεινε μετάφρασιν');
            }
        });
    });
    
});
